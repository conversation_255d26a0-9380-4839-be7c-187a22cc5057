<!--
 * @Description: 现代化自定义悬浮异形tabbar
-->
<template>
	<view class="tqb-tabbar">
		<view class="mark" v-if="visible" @click="visible = false">
			<view class="mark-title">
				<view class="time">
					<view>{{ currentDate.day }}</view>
					<view class="may">{{ currentDate.month }}</view>
				</view>
				<view class="label">说说 记录美好生活</view>
			</view>
			<view class="mak-box">
				<view class="box"
					v-for="(item, index) in handleList"
					:key="index"
					@click="handleClick(item)"
					:style="{ 'animation-delay': (index * 0.1) + 's' }">
					<view class="img-view">
						<image :src="item.imageUrl" mode=""></image>
					</view>
					<text>{{ item.text }}</text>
				</view>
				<view class="close" @click="handleClose">
					<image src="/static/yl_welore/style/icon/laji.png" mode=""></image>
				</view>
			</view>
		</view>
		<view class="tqb-tabbar-body">
			<view class="tqb-tabbar-item" v-for="(item, index) in computedTabBarList" :key="index"
				@click="switchClick(item, index)">
				<view v-if="index !== 2" class="tabbar-item-body">
					<view class="tabbar-item-icon">
						<image :src="activeIndex == index ? item.selectedIconPath : item.iconPath"
							style="width: 56rpx; height: 56rpx;"></image>
					</view>
					<view :class="[activeIndex == index ? 'active' : '', 'tab-bar-text']">{{ item.text }}</view>
				</view>
				<view v-else class="add-icon">
					<image :src="item.iconPath" style="width: 88rpx; height: 88rpx;"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
var app = getApp();

export default {
	props: {
		activeIndex: {
			type: Number,
			default: 0
		},
		tabbar: {
			type: Object,
			default: () => ({})
		},
		floorstatus: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			tabBarList: [{
				text: '首页',
				iconPath: '/static/yl_welore/style/icon/1.png',
				selectedIconPath: '/static/yl_welore/style/icon/2.png',
				pagePath: '/yl_welore/pages/index/index',
			}, {
				text: '圈子',
				iconPath: '/static/yl_welore/style/icon/3.png',
				selectedIconPath: '/static/yl_welore/style/icon/4.png',
				pagePath: '/yl_welore/pages/circle/index',
			}, {
				text: '发布',
				iconPath: '/static/yl_welore/style/icon/home_add.png',
			}, {
				text: '元宇宙',
				iconPath: '/static/yl_welore/style/icon/a.png',
				selectedIconPath: '/static/yl_welore/style/icon/b.png',
				pagePath: '/yl_welore/pages/square/index',
			}, {
				text: '我的',
				iconPath: '/static/yl_welore/style/icon/default.png',
				selectedIconPath: '/static/yl_welore/style/icon/pos.png',
				pagePath: '/yl_welore/pages/user/index',
			}],
			handleList: [],
			visible: false,
		}
	},
	computed: {
		// 根据传入的tabbar数据动态生成tabBarList
		computedTabBarList() {
			if (this.tabbar && this.tabbar.list && this.tabbar.list.length > 0) {
				return this.tabbar.list.map((item, index) => {
					if (index === 2) {
						// 中间的特殊按钮
						return {
							text: item.text || '发布',
							iconPath: item.iconPath || '/static/yl_welore/style/icon/home_add.png',
							isSpecial: true
						}
					} else {
						return {
							text: item.text,
							iconPath: item.iconPath,
							selectedIconPath: item.selectedIconPath,
							pagePath: item.pagePath,
							isSpecial: false
						}
					}
				});
			}
			return this.tabBarList;
		},
		// 获取当前日期
		currentDate() {
			const now = new Date();
			const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
			return {
				day: String(now.getMonth() + 1).padStart(2, '0') + '-' + String(now.getDate()).padStart(2, '0'),
				month: months[now.getMonth()]
			};
		}
	},
	watch: {
		visible: function (d) {
			// todo anything
		}
	},
	mounted() {
		// 组件挂载时动态生成handleList
		setTimeout(() => {
			this.generateHandleList();
		}, 1500)
	},
	methods: {
		// 根据copyright动态生成handleList
		generateHandleList() {
			const copyright = app.globalData.store.$state.copyright;
			console.log(copyright);
			var handleList = [];
			if (copyright.version == 1) {
				handleList = [{
					imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.writing.images,
					text: app.globalData.store.$state.diy.pattern_data.release.list.writing.title,
					url: `/yl_welore/pages/packageA/add/index?type=yuyin`
				}];
			} else {
				handleList.push({
					imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.writing.images,
					text: app.globalData.store.$state.diy.pattern_data.release.list.writing.title,
					url: `/yl_welore/pages/packageA/add/index?type=yuyin`
				});
				//发布音频
				if (copyright.hair_audio_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.audio.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.audio.title,
						url: `/yl_welore/pages/packageA/add/index?type=yuyin`
					});
				}
				//发布投票
				if (copyright.hair_vote_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.graffito.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.graffito.title,
						url: `/yl_welore/pages/packageA/add/index?type=toupiao`
					});
				}
				//发布视频
				if (copyright.hair_video_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.video.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.video.title,
						url: `/yl_welore/pages/packageA/add/index?type=shipin`
					});
				}
				//发布活动
				if (copyright.hair_brisk_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.brisk.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.brisk.title,
						url: `/yl_welore/pages/packageA/add/index?type=huodong`
					});
				}
			}
			this.handleList = handleList;
		},
		switchClick(data, index) {
			if (index == this.activeIndex) {
				return
			}
			if (index === 2) {
				this.visible = !this.visible;

				if (this.activeIndex == 1) {
					uni.$emit('hideCanvas', this.visible)
				}
				return
			}
			this.handleClose()

			// 发出事件给父组件处理导航
			this.$emit('tabbar-switch', {
				index: index,
				item: data
			});

			// 如果有pagePath，使用switchTab导航
			if (data.pagePath) {
				uni.switchTab({
					url: data.pagePath
				});
			}
		},
		handleClose() {
			this.visible = false;
			if (this.activeIndex == 1) {
				uni.$emit('hideCanvas', false)
			}
		},
		handleOpenPage(item) {
			let that = this
			setTimeout(function () {
				that.handleClose()
			}, 500)
			uni.navigateTo({
				url: item.url
			})
		},
		handleClick(item) {
			let that = this
			that.handleOpenPage(item)
		},
		// 刷新handleList的公共方法，供父组件调用
		refreshHandleList() {
			this.generateHandleList();
		}
	}
}
</script>

<style lang="scss" scoped>
.tqb-tabbar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 100000;
}

.tqb-tabbar-body {
	border-radius: 80rpx;
	height: 120rpx;
	display: flex;
	background: linear-gradient(to right, #faf7fb, #eef5fa, #f2fbfc);
	position: fixed;
	bottom: 40rpx;
	width: 96%;
	left: 0;
	right: 0;
	z-index: 1001;
	padding-top: 20rpx;
	margin: 0 auto;
	box-shadow: 0px 0px 22rpx 9rpx rgba(0, 0, 0, 0.1);

	.tqb-tabbar-item {
		flex: 1;
		display: flex;
		justify-content: center;
		text-align: center;

		.tabbar-item-body {}

		.tabbar-item-icon {
			display: flex;
			justify-content: center;
		}

		.tab-bar-text {
			color: #999999;
			font-size: 22rpx;
			margin-top: 9rpx;
		}

		.active {
			color: #645AEE;
		}

		.add-icon {
			width: 88rpx;
			height: 88rpx;
			position: relative;
			top: -8rpx;
		}
	}
}

.mark {
	position: fixed;
	color: #fff;
	background-image: url(https://img0.baidu.com/it/u=429448692,4137220192&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=1045);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	font-size: 26rpx;
	z-index: 999;
	text-align: center;

	.mark-title {
		position: relative;
		top: 100px;
		width: auto;
		left: 35px;
		color: #484949;
		text-align: left;
		animation: example1 0.3s ease-out 0.3s backwards;

		.time {
			display: flex;
			font-size: 35px;
			font-weight: bold;

			.may {
				font-size: 24rpx;
				margin-left: 5rpx;
			}
		}

		.label {
			margin-top: 10rpx;
			font-size: 28rpx;
		}
	}
}

.mak-box {
	position: absolute;
	bottom: 100px;
	left: 0;
	right: 0;
	color: #484949;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 0 40rpx;

	.box {
		width: 30%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-bottom: 20px;
		animation: example1 0.4s ease-out 0.4s backwards;

		.img-view {
			width: 58px;
			height: 58px;
			border-radius: 50%;
			margin-bottom: 10rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		image {
			width: 30px;
			height: 30px;
			border-radius: 50%;
		}

		text {
			font-size: 28rpx;
		}
	}
}

.close {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	animation: example1 0.5s ease-out 0.5s backwards;

	image {
		width: 40px;
		height: 40px;
	}
}

@keyframes example1 {
	0% {
		transform: translate(-60px);
		opacity: 0;
	}

	50% {
		transform: translate(0);
		opacity: 1;
	}

	100% {
		transform: translate(0);
		opacity: 1;
	}
}
</style>
