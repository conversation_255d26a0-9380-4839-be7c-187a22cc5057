<!--
 * @Description: 现代化自定义悬浮异形tabbar
-->
<template>
	<view class="tqb-tabbar">
		<view class="mark" v-if="visible" @click="visible = false">
			<view class="mark-title">
				<view class="time-container">
					<view class="time">
						<view class="day">{{ currentDate.day }}</view>
						<view class="month">{{ currentDate.month }}</view>
					</view>
					<view class="year">{{ currentDate.year }}</view>
				</view>
				<view class="content">
					<view class="label">说说 记录美好生活</view>
					<view class="subtitle">分享此刻的精彩瞬间</view>
				</view>
				<view class="decoration">
					<view class="dot"></view>
					<view class="line"></view>
				</view>
			</view>
			<view class="mak-box">
				<view class="box" v-for="(item, index) in handleList" :key="index" @click="handleClick(item.url)"
					:style="{ 'animation-delay': (index * 0.1) + 's' }">
					<view class="img-view">
						<image :src="item.imageUrl" mode=""></image>
						<image v-if="item.vip == 1" class="vip-icon" src="/static/yl_welore/style/icon/left_vip.png" mode=""></image>
					</view>
					<text>{{ item.text }}</text>
				</view>
				<view class="close" @click="handleClose">
					<text class="_icon-close-round" style="font-size: 80rpx;"></text>
				</view>
			</view>
		</view>
		<view class="tqb-tabbar-body">
			<view class="tqb-tabbar-item" v-for="(item, index) in computedTabBarList" :key="index"
				@click="switchClick(item, index)">
				<view v-if="index !== 2" class="tabbar-item-body">
					<view class="tabbar-item-icon">
						<image :src="activeIndex == index ? item.selectedIconPath : item.iconPath"
							style="width: 56rpx; height: 56rpx;"></image>
					</view>
					<view
						:style="{ 'font-size: 24rpx;color': activeIndex == index ? tabbar.selectedColor : tabbar.color }">
						{{ item.text }}</view>
				</view>
				<view v-else class="add-icon">
					<image :src="item.iconPath" style="width: 88rpx; height: 88rpx;"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
var app = getApp();
var http = require("@/yl_welore/util/http.js");
export default {
	props: {
		activeIndex: {
			type: Number,
			default: 0
		},
		tabbar: {
			type: Object,
			default: () => ({})
		},
		floorstatus: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			user_info: {},
			tabBarList: [],
			handleList: [],
			visible: false,
			diy:{},
			version:1,
			add:{}
		}
	},
	computed: {
		// 根据传入的tabbar数据动态生成tabBarList
		computedTabBarList() {
			if (this.tabbar && this.tabbar.list && this.tabbar.list.length > 0) {
				return this.tabbar.list.map((item, index) => {
					if (index === 2) {
						// 中间的特殊按钮
						return {
							text: item.text || '发布',
							iconPath: item.iconPath || '/static/yl_welore/style/icon/home_add.png',
							isSpecial: true
						}
					} else {
						return {
							home: item.home_s,
							text: item.text,
							iconPath: item.iconPath,
							selectedIconPath: item.selectedIconPath,
							pagePath: item.pagePath,
							isSpecial: false
						}
					}
				});
			}
			return this.tabBarList;
		},
		// 获取当前日期
		currentDate() {
			const now = new Date();
			const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
			return {
				day: String(now.getMonth() + 1).padStart(2, '0') + '-' + String(now.getDate()).padStart(2, '0'),
				month: months[now.getMonth()],
				year: now.getFullYear()
			};
		}
	},
	watch: {
		visible: function (d) {
			// todo anything
		}
	},
	mounted() {
		console.log(this.tabbar);
		// 组件挂载时动态生成handleList
		setTimeout(() => {
			this.generateHandleList();
			this.get_user_info();
		}, 1500)
	},
	methods: {
		// 根据copyright动态生成handleList
		generateHandleList() {
			const copyright = app.globalData.store.$state.copyright;
			const diy=app.globalData.store.$state.diy;
			console.log(diy);
			var handleList = [];
			if (copyright.version == 1) {
				handleList = [{
					imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.writing.images,
					text: app.globalData.store.$state.diy.pattern_data.release.list.writing.title,
					url: `tuwen`,
					vip:0,
				}];
			} else {
				handleList.push({
					imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.writing.images,
					text: app.globalData.store.$state.diy.pattern_data.release.list.writing.title,
					url: `tuwen`,
					vip:0,
				});
				//发布音频
				if (copyright.hair_audio_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.audio.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.audio.title,
						url: `yuyin`,
						vip:diy.user_vip.voice_member,
					});
				}
				//发布投票
				if (copyright.hair_vote_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.graffito.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.graffito.title,
						url: `toupiao`,
						vip:diy.user_vip.vote_member,
					});
				}
				//发布视频
				if (copyright.hair_video_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.video.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.video.title,
						url: `shipin`,
						vip:diy.user_vip.video_member,
					});
				}
				//发布活动
				if (copyright.hair_brisk_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.brisk.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.brisk.title,
						url: `huodong`,
						vip:diy.user_vip.brisk_member,
					});
				}
			}
			this.handleList = handleList;
		},
		switchClick(data, index) {
			console.log(data);
			console.log(index);
			console.log(this.activeIndex);
			if (index == this.activeIndex) {
				return
			}
			if (index === 2) {
				this.visible = !this.visible;

				if (this.activeIndex == 1) {
					uni.$emit('hideCanvas', this.visible)
				}
				return
			}
			if (data.home == 1) {
				uni.navigateTo({
					url: data.pagePath
				});
			} else {
				uni.switchTab({
					url: data.pagePath
				});
			}
		},
		handleClose() {
			this.visible = false;
			if (this.activeIndex == 1) {
				uni.$emit('hideCanvas', false)
			}
		},
		get_user_info () {
			var b = app.globalData.api_root + 'User/get_user_info';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			http.POST(b, {
				params: params,
				success: function (res) {
					console.log('user_info', res);
					if (res.data.status == 'success') {
						that.user_info = res.data.info;
						//that.get_diy();
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						});
					}
				},
				fail: function () {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: function (res) { }
					});
				}
			});
		},
		get_diy () {
			var b = app.globalData.api_root + 'User/get_diy';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.uid = e.uid;
			params.token = e.token;
			params.openid = e.openid;
			http.POST(b, {
				params: params,
				success: function (res) {
					console.log(res);
					if (res.data.status) {
						return;
					} else {
						that.version = res.data.version;
						that.diy = res.data;
						that.add = res.data.pattern_data.release.list;
					}
				},
				fail: function () {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: function (res) { }
					});
				}
			});
		},
		handleClick(k) {
			const copyright = app.globalData.store.$state.copyright;
			const user_info = this.user_info;
			if (k == 'tuwen') {
				uni.navigateTo({
					url: '/yl_welore/pages/packageA/add/index?type=0&fa_class=0&name=&gambit_name=&gambit_id=0'
				});
			}
			if (k == 'toupiao') {
				if (copyright['vote_member'] == 1) {
					if (user_info['is_vip'] == 1) {
						uni.navigateTo({
							url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=0&name=&gambit_name=&gambit_id=0'
						});
					} else {
						uni.showToast({
							title: '此功能仅限VIP用户使用',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				} else {
					uni.navigateTo({
						url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=0&name=&gambit_name=&gambit_id=0'
					});
				}
			}
			if (k == 'yuyin') {
				if (copyright['voice_member'] == 1) {
					if (user_info['is_vip'] == 1) {
						uni.navigateTo({
							url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=0&name=&gambit_name=&gambit_id=0'
						});
					} else {
						uni.showToast({
							title: '此功能仅限VIP用户使用',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				} else {
					uni.navigateTo({
						url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=0&name=&gambit_name=&gambit_id=0'
					});
				}
			}
			if (k == 'shipin') {
				if (copyright['video_member'] == 1) {
					if (user_info['is_vip'] == 1) {
						uni.navigateTo({
							url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=0&name=&gambit_name=&gambit_id=0'
						});
					} else {
						uni.showToast({
							title: '此功能仅限VIP用户使用',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				} else {
					uni.navigateTo({
						url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=0&name=&gambit_name=&gambit_id=0'
					});
				}
			}
			if (k == 'huodong') {
				if (copyright['brisk_member'] == 1) {
					if (user_info['is_vip'] == 1) {
						uni.navigateTo({
							url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=0&name=&gambit_name=&gambit_id=0'
						});
					} else {
						uni.showToast({
							title: '此功能仅限VIP用户使用',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				} else {
					uni.navigateTo({
						url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=0&name=&gambit_name=&gambit_id=0'
					});
				}
			}
		},
		// 刷新handleList的公共方法，供父组件调用
		refreshHandleList() {
			this.generateHandleList();
		}
	}
}
</script>

<style lang="scss" scoped>
.tqb-tabbar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 100000;
}

.tqb-tabbar-body {
	border-radius: 80rpx;
	height: 130rpx;
	display: flex;
	background: linear-gradient(to right, #f5f0f7, #e8f0f6, #edf7f9);
	position: fixed;
	bottom: 40rpx;
	width: 91%;
	left: 0;
	right: 0;
	z-index: 1001;
	padding-top: 20rpx;
	margin: 0 auto;
	box-shadow: 0px 0px 22rpx 9rpx rgba(0, 0, 0, 0.1);

	.tqb-tabbar-item {
		flex: 1;
		display: flex;
		justify-content: center;
		text-align: center;

		.tabbar-item-body {}

		.tabbar-item-icon {
			display: flex;
			justify-content: center;
			margin-bottom: 10rpx;
		}

		.tab-bar-text {
			color: #999999;
			font-size: 22rpx;
			margin-top: 9rpx;
		}

		.active {
			color: #645AEE;
		}

		.add-icon {
			width: 88rpx;
			height: 88rpx;
			position: relative;
		}
	}
}

.mark {
	position: fixed;
	color: #333;
	background: linear-gradient(135deg, #f0f2ff 0%, #faf0ff 100%);
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	font-size: 26rpx;
	z-index: 2000;
	text-align: center;

	.mark-title {
		position: relative;
		top: 80px;
		left: 35px;
		color: #484949;
		text-align: left;
		animation: slideInFromLeft 0.6s ease-out 0.2s backwards;

		.time-container {
			display: flex;
			align-items: baseline;
			margin-bottom: 20rpx;

			.time {
				display: flex;
				align-items: baseline;
				margin-right: 20rpx;

				.day {
					font-size: 72rpx;
					font-weight: bold;
					line-height: 1;
				}

				.month {
					font-size: 28rpx;
					margin-left: 8rpx;
					opacity: 0.8;
					font-weight: 500;
				}
			}

			.year {
				font-size: 24rpx;
				opacity: 0.6;
				font-weight: 300;
			}
		}

		.content {
			.label {
				font-size: 32rpx;
				font-weight: 600;
				margin-bottom: 8rpx;
				line-height: 1.2;
			}

			.subtitle {
				font-size: 24rpx;
				opacity: 0.7;
				font-weight: 300;
			}
		}

		.decoration {
			position: absolute;
			right: -20rpx;
			top: 50%;
			transform: translateY(-50%);
			display: flex;
			align-items: center;

			.dot {
				width: 12rpx;
				height: 12rpx;
				background: #484949;
				border-radius: 50%;
				opacity: 0.6;
			}

			.line {
				width: 60rpx;
				height: 2rpx;
				background: linear-gradient(to right, #484949, transparent);
				margin-left: 8rpx;
				opacity: 0.4;
			}
		}
	}
}

.mak-box {
	position: absolute;
	bottom: 100px;
	left: 0;
	right: 0;
	color: #484949;
	display: flex;
	flex-wrap: wrap;
	justify-content: start;
	padding: 0 40rpx;
	gap: 30rpx;

	.box {
		width: 30%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
		animation: example1 0.4s ease-out 0.4s backwards;

		.img-view {
			width: 58px;
			height: 58px;
			border-radius: 50%;
			margin-bottom: 10rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		image {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
		}

		text {
			font-size: 28rpx;
		}
	}
}

.close {
	margin-top: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	animation: example1 0.5s ease-out 0.5s backwards;

	image {
		width: 40px;
		height: 40px;
	}
}

@keyframes example1 {
	0% {
		transform: translateY(-60px);
		opacity: 0;
	}

	50% {
		transform: translateY(0);
		opacity: 1;
	}

	100% {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes slideInFromLeft {
	0% {
		transform: translateX(-80px);
		opacity: 0;
	}

	60% {
		transform: translateX(10px);
		opacity: 0.8;
	}

	100% {
		transform: translateX(0);
		opacity: 1;
	}
}
</style>
