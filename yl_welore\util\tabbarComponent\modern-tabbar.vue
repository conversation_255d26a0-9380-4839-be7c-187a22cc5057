<!--
 * @Description: 现代化自定义悬浮异形tabbar
-->
<template>
	<view class="tqb-tabbar">
		<view class="tqb-tabbar-popup" v-if="visible" @click="visible = false">
			<!-- 关闭按钮 -->
			<view class="close-btn" @click="handleClose">
				<view class="close-icon">
					<text class="close-text">×</text>
				</view>
			</view>

			<!-- 弹出内容区域 -->
			<view class="popup-content">
				<view class="popup-title">选择发布类型</view>
				<view class="popup-grid">
					<view class="popup-item"
						v-for="(item, index) in handleList"
						:key="index"
						@click="handleClick(item)"
						:style="{ 'animation-delay': (index * 0.1) + 's' }">
						<view class="popup-item-icon">
							<image :src="item.imageUrl" class="item-image"></image>
						</view>
						<view class="popup-item-text">
							{{ item.text }}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="tqb-tabbar-body">
			<view class="tqb-tabbar-item" v-for="(item, index) in computedTabBarList" :key="index"
				@click="switchClick(item, index)">
				<view v-if="index !== 2" class="tabbar-item-body">
					<view class="tabbar-item-icon">
						<image :src="activeIndex == index ? item.selectedIconPath : item.iconPath"
							style="width: 56rpx; height: 56rpx;"></image>
					</view>
					<view :class="[activeIndex == index ? 'active' : '', 'tab-bar-text']">{{ item.text }}</view>
				</view>
				<view v-else class="add-icon">
					<image :src="item.iconPath" style="width: 88rpx; height: 88rpx;"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
var app = getApp();

export default {
	props: {
		activeIndex: {
			type: Number,
			default: 0
		},
		tabbar: {
			type: Object,
			default: () => ({})
		},
		floorstatus: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			tabBarList: [{
				text: '首页',
				iconPath: '/static/yl_welore/style/icon/1.png',
				selectedIconPath: '/static/yl_welore/style/icon/2.png',
				pagePath: '/yl_welore/pages/index/index',
			}, {
				text: '圈子',
				iconPath: '/static/yl_welore/style/icon/3.png',
				selectedIconPath: '/static/yl_welore/style/icon/4.png',
				pagePath: '/yl_welore/pages/circle/index',
			}, {
				text: '发布',
				iconPath: '/static/yl_welore/style/icon/home_add.png',
			}, {
				text: '元宇宙',
				iconPath: '/static/yl_welore/style/icon/a.png',
				selectedIconPath: '/static/yl_welore/style/icon/b.png',
				pagePath: '/yl_welore/pages/square/index',
			}, {
				text: '我的',
				iconPath: '/static/yl_welore/style/icon/default.png',
				selectedIconPath: '/static/yl_welore/style/icon/pos.png',
				pagePath: '/yl_welore/pages/user/index',
			}],
			handleList: [],
			visible: false,
		}
	},
	computed: {
		// 根据传入的tabbar数据动态生成tabBarList
		computedTabBarList() {
			if (this.tabbar && this.tabbar.list && this.tabbar.list.length > 0) {
				return this.tabbar.list.map((item, index) => {
					if (index === 2) {
						// 中间的特殊按钮
						return {
							text: item.text || '发布',
							iconPath: item.iconPath || '/static/yl_welore/style/icon/home_add.png',
							isSpecial: true
						}
					} else {
						return {
							text: item.text,
							iconPath: item.iconPath,
							selectedIconPath: item.selectedIconPath,
							pagePath: item.pagePath,
							isSpecial: false
						}
					}
				});
			}
			return this.tabBarList;
		}
	},
	watch: {
		visible: function (d) {
			// todo anything
		}
	},
	mounted() {
		// 组件挂载时动态生成handleList
		setTimeout(() => {
			this.generateHandleList();
		}, 1500)
	},
	methods: {
		// 根据copyright动态生成handleList
		generateHandleList() {
			const copyright = app.globalData.store.$state.copyright;
			console.log(copyright);
			var handleList = [];
			if (copyright.version == 1) {
				handleList = [{
					imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.writing.images,
					text: app.globalData.store.$state.diy.pattern_data.release.list.writing.title,
					url: `/yl_welore/pages/packageA/add/index?type=yuyin`
				}];
			} else {
				handleList.push({
					imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.writing.images,
					text: app.globalData.store.$state.diy.pattern_data.release.list.writing.title,
					url: `/yl_welore/pages/packageA/add/index?type=yuyin`
				});
				//发布音频
				if (copyright.hair_audio_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.audio.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.audio.title,
						url: `/yl_welore/pages/packageA/add/index?type=yuyin`
					});
				}
				//发布投票
				if (copyright.hair_vote_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.graffito.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.graffito.title,
						url: `/yl_welore/pages/packageA/add/index?type=toupiao`
					});
				}
				//发布视频
				if (copyright.hair_video_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.video.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.video.title,
						url: `/yl_welore/pages/packageA/add/index?type=shipin`
					});
				}
				//发布活动
				if (copyright.hair_brisk_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.brisk.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.brisk.title,
						url: `/yl_welore/pages/packageA/add/index?type=huodong`
					});
				}
			}
			this.handleList = handleList;
		},
		switchClick(data, index) {
			if (index == this.activeIndex) {
				return
			}
			if (index === 2) {
				this.visible = !this.visible;

				if (this.activeIndex == 1) {
					uni.$emit('hideCanvas', this.visible)
				}
				return
			}
			this.handleClose()

			// 发出事件给父组件处理导航
			this.$emit('tabbar-switch', {
				index: index,
				item: data
			});

			// 如果有pagePath，使用switchTab导航
			if (data.pagePath) {
				uni.switchTab({
					url: data.pagePath
				});
			}
		},
		handleClose() {
			this.visible = false;
			if (this.activeIndex == 1) {
				uni.$emit('hideCanvas', false)
			}
		},
		handleOpenPage(item) {
			let that = this
			setTimeout(function () {
				that.handleClose()
			}, 500)
			uni.navigateTo({
				url: item.url
			})
		},
		handleClick(item) {
			let that = this
			that.handleOpenPage(item)
		},
		// 刷新handleList的公共方法，供父组件调用
		refreshHandleList() {
			this.generateHandleList();
		}
	}
}
</script>

<style lang="scss" scoped>
.tqb-tabbar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 1000;
}

.tqb-tabbar-body {
	border-radius: 80rpx;
	height: 120rpx;
	display: flex;
	background: linear-gradient(to right, #faf7fb, #eef5fa, #f2fbfc);
	position: fixed;
	bottom: 40rpx;
	width: 96%;
	left: 0;
	right: 0;
	z-index: 1001;
	padding-top: 20rpx;
	margin: 0 auto;
	box-shadow: 0px 0px 22rpx 9rpx rgba(0, 0, 0, 0.1);

	.tqb-tabbar-item {
		flex: 1;
		display: flex;
		justify-content: center;
		text-align: center;

		.tabbar-item-body {}

		.tabbar-item-icon {
			display: flex;
			justify-content: center;
		}

		.tab-bar-text {
			color: #999999;
			font-size: 22rpx;
			margin-top: 9rpx;
		}

		.active {
			color: #645AEE;
		}

		.add-icon {
			width: 88rpx;
			height: 88rpx;
			position: relative;
			top: -8rpx;
		}
	}
}

.tqb-tabbar-popup {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(10rpx);
	z-index: 9999;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	animation: fadeIn 0.3s ease-out;

	.close-btn {
		position: absolute;
		top: 80rpx;
		right: 40rpx;
		z-index: 10000;
		animation: slideInDown 0.4s ease-out;

		.close-icon {
			width: 80rpx;
			height: 80rpx;
			background: rgba(255, 255, 255, 0.9);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);

			.close-text {
				font-size: 48rpx;
				color: #666;
				font-weight: 300;
				line-height: 1;
			}
		}
	}

	.popup-content {
		background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
		border-radius: 40rpx 40rpx 0 0;
		padding: 60rpx 40rpx 100rpx;
		animation: slideInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.1);

		.popup-title {
			text-align: center;
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 60rpx;
		}

		.popup-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(160rpx, 1fr));
			gap: 40rpx;
			justify-items: center;

			.popup-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 30rpx 20rpx;
				border-radius: 24rpx;
				background: linear-gradient(135deg, #fff 0%, #f5f7ff 100%);
				box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
				transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
				animation: popIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
				min-width: 140rpx;

				&:hover {
					transform: translateY(-8rpx) scale(1.05);
					box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.15);
				}

				&:active {
					transform: translateY(-4rpx) scale(0.98);
				}

				.popup-item-icon {
					margin-bottom: 20rpx;

					.item-image {
						width: 80rpx;
						height: 80rpx;
						border-radius: 16rpx;
						transition: transform 0.3s ease;
					}
				}

				.popup-item-text {
					font-size: 26rpx;
					color: #333;
					font-weight: 500;
					text-align: center;
					line-height: 1.2;
				}
			}
		}
	}
}

/* 动画定义 */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideInUp {
	from {
		transform: translateY(100%);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes slideInDown {
	from {
		transform: translateY(-100rpx);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes popIn {
	0% {
		transform: scale(0.8) translateY(40rpx);
		opacity: 0;
	}
	50% {
		transform: scale(1.05) translateY(-10rpx);
	}
	100% {
		transform: scale(1) translateY(0);
		opacity: 1;
	}
}
</style>
